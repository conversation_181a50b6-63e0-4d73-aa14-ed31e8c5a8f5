import mongoose, { Document, Schema } from 'mongoose';

export interface IKey extends Document {
  keyValue: string;
  expirationDate: Date;
  ownerId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  description?: string;
}

const KeySchema = new Schema<IKey>({
  keyValue: {
    type: String,
    required: [true, 'Key value is required'],
    unique: true,
    trim: true
  },
  expirationDate: {
    type: Date,
    required: [true, 'Expiration date is required'],
    validate: {
      validator: function(value: Date) {
        return value > new Date();
      },
      message: 'Expiration date must be in the future'
    }
  },
  ownerId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Owner ID is required']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'Description cannot exceed 200 characters']
  }
}, {
  timestamps: true
});

KeySchema.index({ keyValue: 1 });
KeySchema.index({ ownerId: 1 });
KeySchema.index({ expirationDate: 1 });
KeySchema.index({ isActive: 1 });

KeySchema.methods.isExpired = function() {
  return new Date() > this.expirationDate;
};

KeySchema.methods.extendExpiration = function(days: number) {
  const newExpirationDate = new Date(this.expirationDate);
  newExpirationDate.setDate(newExpirationDate.getDate() + days);
  this.expirationDate = newExpirationDate;
  return this.save();
};

export default mongoose.models.Key || mongoose.model<IKey>('Key', KeySchema);
