/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/keys/route";
exports.ids = ["app/api/keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ok_Desktop_Edvinity_Key_dashboard_key_manager_dashboard_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/keys/route.ts */ \"(rsc)/./src/app/api/keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/keys/route\",\n        pathname: \"/api/keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Edvinity Key dashboard\\\\key-manager-dashboard\\\\src\\\\app\\\\api\\\\keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ok_Desktop_Edvinity_Key_dashboard_key_manager_dashboard_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/keys/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/keys/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Key__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Key */ \"(rsc)/./src/models/Key.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _lib_middleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/middleware */ \"(rsc)/./src/lib/middleware.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\nconst createKeySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().optional(),\n    expirationDays: zod__WEBPACK_IMPORTED_MODULE_5__.z.number().min(1).max(365).default(30),\n    ownerId: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().optional()\n});\nasync function handleGET(req) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { searchParams } = new URL(req.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '10');\n        const search = searchParams.get('search') || '';\n        const status = searchParams.get('status') || 'all';\n        let query = {};\n        if (req.user.role === 'buyer') {\n            query.ownerId = req.user.userId;\n        }\n        if (search) {\n            query.$or = [\n                {\n                    keyValue: {\n                        $regex: search,\n                        $options: 'i'\n                    }\n                },\n                {\n                    description: {\n                        $regex: search,\n                        $options: 'i'\n                    }\n                }\n            ];\n        }\n        if (status === 'active') {\n            query.isActive = true;\n            query.expirationDate = {\n                $gt: new Date()\n            };\n        } else if (status === 'expired') {\n            query.expirationDate = {\n                $lte: new Date()\n            };\n        } else if (status === 'inactive') {\n            query.isActive = false;\n        }\n        const skip = (page - 1) * limit;\n        const [keys, total] = await Promise.all([\n            _models_Key__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find(query).populate('ownerId', 'username email role').sort({\n                createdAt: -1\n            }).skip(skip).limit(limit),\n            _models_Key__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query)\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            keys,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Get keys error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handlePOST(req) {\n    try {\n        if (req.user.role !== 'developer') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Only developers can create keys'\n            }, {\n                status: 403\n            });\n        }\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const body = await req.json();\n        const { description, expirationDays, ownerId } = createKeySchema.parse(body);\n        const finalOwnerId = ownerId || req.user.userId;\n        const owner = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(finalOwnerId);\n        if (!owner) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Owner not found'\n            }, {\n                status: 404\n            });\n        }\n        const keyValue = (0,crypto__WEBPACK_IMPORTED_MODULE_6__.randomBytes)(32).toString('hex');\n        const expirationDate = new Date();\n        expirationDate.setDate(expirationDate.getDate() + expirationDays);\n        const key = new _models_Key__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            keyValue,\n            expirationDate,\n            ownerId: finalOwnerId,\n            description\n        });\n        await key.save();\n        await key.populate('ownerId', 'username email role');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(key, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Create key error:', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid input',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nconst GET = (0,_lib_middleware__WEBPACK_IMPORTED_MODULE_4__.withAuth)(handleGET);\nconst POST = (0,_lib_middleware__WEBPACK_IMPORTED_MODULE_4__.withAuth)(handlePOST);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canManageKey: () => (/* binding */ canManageKey),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET;\nif (!JWT_SECRET) {\n    throw new Error('Please define the JWT_SECRET environment variable inside .env.local');\n}\nasync function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n}\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: '7d'\n    });\n}\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\nfunction hasPermission(userRole, requiredRole) {\n    const roleHierarchy = {\n        tester: 1,\n        buyer: 2,\n        developer: 3\n    };\n    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n}\nfunction canManageKey(userRole, keyOwnerId, userId) {\n    if (userRole === 'developer') return true;\n    if (userRole === 'buyer' && keyOwnerId === userId) return true;\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/middleware.ts":
/*!*******************************!*\
  !*** ./src/lib/middleware.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withRole: () => (/* binding */ withRole)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nfunction withAuth(handler) {\n    return async (req)=>{\n        try {\n            const token = req.headers.get('authorization')?.replace('Bearer ', '') || req.cookies.get('token')?.value;\n            if (!token) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Authentication required'\n                }, {\n                    status: 401\n                });\n            }\n            const payload = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.verifyToken)(token);\n            if (!payload) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid or expired token'\n                }, {\n                    status: 401\n                });\n            }\n            req.user = payload;\n            return await handler(req);\n        } catch (error) {\n            console.error('Authentication error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication failed'\n            }, {\n                status: 401\n            });\n        }\n    };\n}\nfunction withRole(requiredRole) {\n    return function(handler) {\n        return withAuth(async (req)=>{\n            if (!req.user) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Authentication required'\n                }, {\n                    status: 401\n                });\n            }\n            const roleHierarchy = {\n                tester: 1,\n                buyer: 2,\n                developer: 3\n            };\n            if (roleHierarchy[req.user.role] < roleHierarchy[requiredRole]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Insufficient permissions'\n                }, {\n                    status: 403\n                });\n            }\n            return await handler(req);\n        });\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Key.ts":
/*!***************************!*\
  !*** ./src/models/Key.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst KeySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    keyValue: {\n        type: String,\n        required: [\n            true,\n            'Key value is required'\n        ],\n        unique: true,\n        trim: true\n    },\n    expirationDate: {\n        type: Date,\n        required: [\n            true,\n            'Expiration date is required'\n        ],\n        validate: {\n            validator: function(value) {\n                return value > new Date();\n            },\n            message: 'Expiration date must be in the future'\n        }\n    },\n    ownerId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: 'User',\n        required: [\n            true,\n            'Owner ID is required'\n        ]\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    description: {\n        type: String,\n        trim: true,\n        maxlength: [\n            200,\n            'Description cannot exceed 200 characters'\n        ]\n    }\n}, {\n    timestamps: true\n});\nKeySchema.index({\n    keyValue: 1\n});\nKeySchema.index({\n    ownerId: 1\n});\nKeySchema.index({\n    expirationDate: 1\n});\nKeySchema.index({\n    isActive: 1\n});\nKeySchema.methods.isExpired = function() {\n    return new Date() > this.expirationDate;\n};\nKeySchema.methods.extendExpiration = function(days) {\n    const newExpirationDate = new Date(this.expirationDate);\n    newExpirationDate.setDate(newExpirationDate.getDate() + days);\n    this.expirationDate = newExpirationDate;\n    return this.save();\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Key || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Key', KeySchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Key.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    username: {\n        type: String,\n        required: [\n            true,\n            'Username is required'\n        ],\n        unique: true,\n        trim: true,\n        minlength: [\n            3,\n            'Username must be at least 3 characters long'\n        ],\n        maxlength: [\n            30,\n            'Username cannot exceed 30 characters'\n        ]\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            'Email is required'\n        ],\n        unique: true,\n        trim: true,\n        lowercase: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            'Please enter a valid email'\n        ]\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            'Password is required'\n        ],\n        minlength: [\n            6,\n            'Password must be at least 6 characters long'\n        ]\n    },\n    role: {\n        type: String,\n        enum: [\n            'tester',\n            'buyer',\n            'developer'\n        ],\n        default: 'tester',\n        required: true\n    }\n}, {\n    timestamps: true\n});\nUserSchema.index({\n    username: 1\n});\nUserSchema.index({\n    email: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cok%5CDesktop%5CEdvinity%20Key%20dashboard%5Ckey-manager-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();